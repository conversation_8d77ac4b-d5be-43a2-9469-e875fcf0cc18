import os
import pandas as pd
import glob
import sys
import chardet
import re
from datetime import datetime

def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma.
    
    Args:
        name: Name string to clean
        
    Returns:
        Cleaned name string
    """
    return str(name).split(',')[0].strip()

def merge_csv_files(input_dir=None, output_file=None, keep_headers=True):
    """
    Merge all CSV files in the specified directory into a single CSV file.

    Parameters:
    - input_dir: Directory containing CSV files to merge
    - output_file: Path to the output merged CSV file (default: merged_YYYYMMDD.csv in input_dir)
    - keep_headers: Whether to keep headers from all files (True) or just the first file (False)
    """
    # If no input directory is provided, ask for it
    if input_dir is None:
        input_dir = input('Enter the directory path containing CSV files to merge: ')
        # Remove quotes if the user included them
        input_dir = input_dir.strip('"\'')

    # Change to the input directory
    try:
        original_dir = os.getcwd()
        os.chdir(input_dir)
        print(f"Changed working directory to: {input_dir}")
    except Exception as e:
        print(f"Error: Could not change to directory {input_dir}")
        print(f"Error details: {str(e)}")
        return False

    # Create merged folder if it doesn't exist
    output_folder = os.path.join(input_dir, "merged")
    if not os.path.exists(output_folder):
        try:
            os.makedirs(output_folder)
            print(f"Created merged folder: {output_folder}")
        except Exception as e:
            print(f"Warning: Could not create merged folder: {str(e)}")
            # Fall back to input directory if merged folder creation fails
            output_folder = input_dir

    # Try to extract conference segment name from the path
    csn = None
    match = re.search(r"\\([\w\s-]+\d{4})\\?", input_dir)
    if match:
        csn = match.group(1)
        print(f"Conference Segment Name (CSN) extracted: {csn}")
    else:
        print("Conference segment name not found in path. Will use generic filename.")

    # If no output file is specified, ask for it or create a default one
    if output_file is None:
        use_default = input('Do you want to specify an output file path? (y/n): ').lower().startswith('y')
        if use_default:
            output_file = input('Enter the output file path: ')
            # Remove quotes if the user included them
            output_file = output_file.strip('"\'')
        else:
            today = datetime.now().strftime("%Y%m%d")
            # Use the output folder for the default file
            if csn:
                output_file = os.path.join(output_folder, f"{csn}_merged_{today}.csv")
            else:
                output_file = os.path.join(output_folder, f"merged_{today}.csv")
            print(f"Using default output file in merged folder: {output_file}")

    # Get a list of all CSV and Excel files in the directory
    csv_files = glob.glob("*.csv")
    excel_files = glob.glob("*.xlsx") + glob.glob("*.xls")

    all_files = csv_files + excel_files

    if not all_files:
        print(f"No CSV or Excel files found in {input_dir}")
        os.chdir(original_dir)  # Change back to original directory
        return False

    print(f"Found {len(csv_files)} CSV files and {len(excel_files)} Excel files to merge")

    # Initialize an empty list to store DataFrames
    all_dfs = []
    total_rows = 0

    # Read each file and append to the list
    for i, file in enumerate(all_files, 1):
        try:
            print(f"Processing file {i}/{len(all_files)}: {file}")

            # Check if the file is an Excel file
            if file.lower().endswith(('.xlsx', '.xls')):
                print(f"  - Detected Excel file format")
                # Try to read the Excel file
                try:
                    df = pd.read_excel(file)
                    rows = len(df)
                    total_rows += rows
                    print(f"  - Read {rows} rows successfully from Excel file")
                    all_dfs.append(df)
                    continue  # Skip to the next file
                except Exception as excel_error:
                    print(f"  - Error reading Excel file: {str(excel_error)}")
                    print(f"  - Will try to read as CSV instead")

            # For CSV files or if Excel reading failed
            # Detect the file encoding using chardet
            with open(file, 'rb') as rawdata:
                # Read the first 100KB to detect encoding
                result = chardet.detect(rawdata.read(100000))
            detected_encoding = result['encoding']
            confidence = result['confidence']
            print(f"  - Detected encoding: {detected_encoding} (confidence: {confidence:.2f})")

            # Use the detected encoding if it's reliable, otherwise fall back to utf-8
            if confidence > 0.7 and detected_encoding is not None:
                encoding_to_use = detected_encoding
            else:
                encoding_to_use = 'utf-8'
                print(f"  - Low confidence detection, falling back to {encoding_to_use}")

            # Try to read the file with the detected encoding
            df = pd.read_csv(file, encoding=encoding_to_use, on_bad_lines='skip', low_memory=False)
            rows = len(df)
            total_rows += rows
            print(f"  - Read {rows} rows successfully with {encoding_to_use} encoding")
            all_dfs.append(df)

        except Exception as e:
            print(f"  - Error reading {file} with detected encoding: {str(e)}")
            try:
                # Try with alternative encodings
                for alt_encoding in ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        print(f"  - Trying with {alt_encoding} encoding")
                        df = pd.read_csv(file, encoding=alt_encoding, on_bad_lines='skip', low_memory=False)
                        rows = len(df)
                        total_rows += rows
                        print(f"  - Read {rows} rows successfully with {alt_encoding} encoding")
                        all_dfs.append(df)
                        break  # Break the loop if successful
                    except Exception as e3:
                        continue  # Try the next encoding
                else:  # This else belongs to the for loop, executes if no break occurred
                    print(f"  - Failed to read file with any encoding")
            except Exception as e2:
                print(f"  - Failed to read file: {str(e2)}")

    if not all_dfs:
        print("No data could be read from any CSV files")
        os.chdir(original_dir)  # Change back to original directory
        return False

    # Standardize column names before combining
    print("Standardizing column names...")
    for i, df in enumerate(all_dfs):
        # Check if the DataFrame has name columns but no 'Author Name' column
        if 'Author Name' not in df.columns:
            # Check for 'Name' column
            if 'Name' in df.columns:
                print(f"  - Renaming 'Name' to 'Author Name' in file {i+1}")
                df.rename(columns={'Name': 'Author Name'}, inplace=True)
            # Check for 'col1' column
            elif 'col1' in df.columns:
                print(f"  - Renaming 'col1' to 'Author Name' in file {i+1}")
                df.rename(columns={'col1': 'Author Name'}, inplace=True)

        # Clean names by removing text after comma
        if 'Author Name' in df.columns:
            print(f"  - Cleaning names in file {i+1}")
            df['Author Name'] = df['Author Name'].apply(clean_name)

    # Combine all DataFrames
    print("Combining all data...")
    if keep_headers:
        # Concatenate all DataFrames, keeping all headers
        combined_df = pd.concat(all_dfs, ignore_index=True)
    else:
        # Use the first DataFrame's columns and append data from others
        combined_df = all_dfs[0]
        for df in all_dfs[1:]:
            combined_df = pd.concat([combined_df, df], ignore_index=True)

    # Check for important columns
    important_columns = []
    email_col = None
    name_cols = []

    for col in combined_df.columns:
        col_lower = col.lower()
        if col_lower == 'email':
            email_col = col
            important_columns.append(col)
        # Now we should only have 'Author Name' for name column, but check others just in case
        if col_lower in ['author name', 'name', 'full name', 'author', 'col1']:
            name_cols.append(col)
            important_columns.append(col)

    # Remove duplicates based on Email column if it exists
    if email_col:
        original_count = len(combined_df)
        print(f"Checking for duplicate emails in column '{email_col}'...")
        # Drop duplicates, keeping the first occurrence
        combined_df = combined_df.drop_duplicates(subset=[email_col], keep='first')
        duplicate_count = original_count - len(combined_df)
        if duplicate_count > 0:
            print(f"Removed {duplicate_count} duplicate email entries")
        else:
            print("No duplicate emails found")
    else:
        print("Warning: No 'Email' column found for deduplication")

    # Report on empty values in name columns but don't remove them
    if name_cols:
        empty_count = combined_df[name_cols].isna().any(axis=1).sum()
        print(f"Found {empty_count} rows with empty values in name columns: {', '.join(name_cols)}")
        print("Note: Rows with empty name values are kept in the output file")

        # If we still have both 'Name' and 'Author Name' columns, merge them
        if 'Name' in combined_df.columns and 'Author Name' in combined_df.columns:
            print("Merging 'Name' and 'Author Name' columns...")
            # Fill NaN values in 'Author Name' with values from 'Name'
            combined_df['Author Name'] = combined_df['Author Name'].fillna(combined_df['Name'])
            # Drop the 'Name' column
            combined_df.drop(columns=['Name'], inplace=True)
            print("'Name' column merged into 'Author Name' and removed")
    else:
        print("Warning: No name columns found ('Author Name', 'Name', etc.)")

    # Write the combined DataFrame to a CSV file with UTF-8-SIG encoding (includes BOM)
    print(f"Writing {len(combined_df)} rows to {output_file}")
    print(f"Using UTF-8-SIG encoding for output file (includes BOM for Excel compatibility)")
    combined_df.to_csv(output_file, index=False, encoding='utf-8-sig')

    print(f"Successfully merged {len(csv_files)} CSV files with a total of {total_rows} rows")
    print(f"Output file: {output_file}")

    # After successfully merging files, create README.md
    readme_content = f"""# Merge Details

## Merge Summary
- **Date of Merge:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Total Files Merged:** {len(all_files)}
- **Total CSV Files:** {len(csv_files)}
- **Total Excel Files:** {len(excel_files)}
- **Total Rows Processed:** {total_rows}
- **Output File:** {os.path.basename(output_file)}

## Files Merged
### CSV Files:
{chr(10).join([f"- {file}" for file in csv_files])}

### Excel Files:
{chr(10).join([f"- {file}" for file in excel_files])}

## Processing Details
- Headers kept from all files: {keep_headers}
- Duplicate emails removed: {duplicate_count if 'duplicate_count' in locals() else 'N/A'}
- Empty name entries: {empty_count if 'empty_count' in locals() else 'N/A'}

## Column Standardization
- 'Name' columns merged into 'Author Name' where applicable
- Email deduplication performed (first occurrence kept)
"""

    # Write README.md to the merged folder
    readme_path = os.path.join(output_folder, "README.md")
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"Created merge documentation: {readme_path}")
    except Exception as e:
        print(f"Warning: Could not create README.md: {str(e)}")

    # Change back to original directory
    os.chdir(original_dir)
    return True

if __name__ == "__main__":
    # Check if directory is provided as command line argument
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
        merge_csv_files(input_dir, output_file)
    else:
        # Use interactive input
        merge_csv_files()
        print("\nPress Enter to exit...")
        input()


